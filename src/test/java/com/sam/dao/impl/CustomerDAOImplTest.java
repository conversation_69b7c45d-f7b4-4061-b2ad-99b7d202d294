package com.sam.dao.impl;

import com.sam.dao.ICustomerDAO;
import com.sam.entity.Customer;
import com.sam.dao.impl.CustomerDAOImpl;
import com.sam.entity.Customer;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Date;

public class CustomerDAOImplTest {

    ICustomerDAO customerDAO=new CustomerDAOImpl();
    SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");
    @Test
    public void selectAll() {
    }

    @Test
    public void insertCustomer() throws Exception{
        Customer customer=new Customer();
        customer.setName("jack");
        Date birthday = sdf.parse("1999-09-09");
        customer.setBirthday(birthday);
        customer.setSex("男");
        customer.setTel("13476453999");
        customer.setAddress("湖北黄冈");
        customer.setIsvip(0);
        customer.setRemark("jack备注信息");
        customer.setCreatetime(new Date());
        customer.setUpdatetime(new Date());
        int n = customerDAO.insertCustomer(customer);
        System.out.println("n = " + n);
    }
}