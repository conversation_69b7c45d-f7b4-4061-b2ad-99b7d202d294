package com.sam.util;

import com.sam.util.ResultCode;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 给前端统一返回的结果，针对业务层的所有方法实现都返回该对象
 */
@Data
@Accessors(chain = true)  //开启链式编程
public class ResultVO<T> {
    //状态码（200=成功，其他=异常）
    private Integer code;
    // 提示信息
    private String message;
    // 业务数据-->传递给前端的数据
    private T data;


    public static  ResultVO<?> success() {
        ResultVO<?> result = new ResultVO<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(ResultCode.SUCCESS.getMessage());
        return result;
    }
    public static  ResultVO<?> success(String message) {
        ResultVO<?> result = new ResultVO<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(ResultCode.SUCCESS.getMessage());
        return result;
    }

    // 快速构建成功/失败结果的静态方法
    public static <T> ResultVO<T> success(T data) {
        ResultVO<T> result = new ResultVO<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(ResultCode.SUCCESS.getMessage());
        result.setData(data);
        return result;
    }

    public static ResultVO<?> error(String message) {
        ResultVO<?> result = new ResultVO<>();
        result.setCode(ResultCode.INTERNAL_ERROR.getCode());
        result.setMessage(message);
        return result;
    }
    public static ResultVO<?> error(int code, String message) {
        ResultVO<?> result = new ResultVO<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}
