package com.sam.util;

import java.io.InputStream;
import java.sql.*;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-06 19:49
 */
public class DBUtil {
    public static final String URL;
    public static final String DRIVER;
    public static final String USER;
    public static final String PASSWORD;

    static{
        try {
            //创建一个空Properties对象
            Properties prop = new Properties();
            //调用load方法加载jdbc.properties文件
            // InputStream is = ClassLoader.getSystemResourceAsStream("jdbc.properties");
            InputStream is =DBUtil.class.getClassLoader().getResourceAsStream("jdbc.properties");
            prop.load(is);
            //从prop对象中获取对应值
            URL=prop.getProperty("url");
            DRIVER=prop.getProperty("driverClass");
            USER=prop.getProperty("user");
            PASSWORD=prop.getProperty("password");
            //步骤1：注册驱动 （static块，在类加载的时候，最先执行，且执行1次）
            Class.forName(DRIVER);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取连接对象
     *
     * @return 数据库连接对象
     */
    public static Connection getConnection() {
        Connection conn = null;
        try {
            conn = DriverManager.getConnection(URL, USER, PASSWORD);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return conn;
    }

    /**
     * 释放数据库连接
     */
    public static void closeResource(ResultSet rs, Statement ps, Connection conn) {

        //如果rs 结果集不为null，则关闭
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

        //如果执行sql语句的对象不为null，则关闭
        if (ps != null) {
            try {
                ps.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        //如果连接对象不为空，则关闭
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
}
