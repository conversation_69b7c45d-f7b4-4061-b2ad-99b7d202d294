package com.sam.controller;

import com.sam.entity.Customer;
import com.sam.service.ICustomerService;
import com.sam.service.impl.CustomerServiceImpl;
import com.sam.util.ResultVO;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/7
 */
@WebServlet(name = "AddCustomerServlet", value = "/AddCustomerServlet")
public class AddCustomerServlet extends HttpServlet {
    //创建业务层对象
    private ICustomerService customerService = new CustomerServiceImpl();
    SimpleDateFormat sdf =new SimpleDateFormat("yyyy-MM-dd");

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 当用户通过GET请求访问时，转发到添加客户的JSP页面
        request.getRequestDispatcher("addCustomer.jsp").forward(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
    //接受页面参数值
        request.setCharacterEncoding("UTF-8");
        String name = request.getParameter("name");
        String birthday = request.getParameter("birthday");
        String sex = request.getParameter("sex");
        String tel = request.getParameter("tel");
        String address = request.getParameter("address");
        String isvipStr = request.getParameter("isvip");
        String remark = request.getParameter("remark");
    //调用Service方法

        //command+option+t,快捷创建try-catch
        Customer customer= null;
        try {
            customer = new Customer();
            customer.setName(name);
            Date birthdayT = sdf.parse(birthday);
            customer.setBirthday(birthdayT);
            customer.setSex(sex);
            customer.setTel(tel);
            customer.setAddress(address);
            // 处理isvip参数，如果没有传递则默认为0
            int isvip = (isvipStr != null) ? Integer.parseInt(isvipStr) : 0;
            customer.setIsvip(isvip);
            customer.setRemark(remark);
            customer.setCreatetime(new Date());
            customer.setUpdatetime(new Date());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        ResultVO resultVO = customerService.registerCustomer(customer);

        //跳转页面
        if(resultVO.getCode() == 200){
            response.sendRedirect(request.getContextPath()+"/CustomerListServlet");
        }else {
            request.setAttribute("r",resultVO);
            request.getRequestDispatcher("addCustomer.jsp").forward(request,response);
        }
    }
}