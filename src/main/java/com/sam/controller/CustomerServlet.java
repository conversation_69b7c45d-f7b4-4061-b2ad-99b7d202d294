package com.sam.controller;

import com.sam.entity.Customer;
import com.sam.service.ICustomerService;
import com.sam.service.impl.CustomerServiceImpl;
import com.sam.util.ResultVO;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

@WebServlet(name = "CustomerServlet", value = "/CustomerServlet")
public class CustomerServlet extends HttpServlet {

    private ICustomerService customerService = new CustomerServiceImpl();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 调用业务逻辑层获取数据
        ResultVO<List<Customer>> resultVO = customerService.findAllCustomers();
        System.out.println("resultVO = " + resultVO);

        // 将数据放入 request 作用域
        request.setAttribute("r", resultVO);

        // 请求转发到 JSP 页面
        request.getRequestDispatcher("customerList.jsp").forward(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        doGet(request, response); // 让 POST 请求也能处理
    }
}