package com.sam.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-06 19:15
 */
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import  java.util.Date;

/**
 * 和customer表对应的实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Customer {
    private Integer id;
    private String name;
    private Date birthday;
    private String sex;
    private String tel;
    private String address;
    private Integer isvip;
    private String remark;
    private Date createtime;
    private Date updatetime;
}
