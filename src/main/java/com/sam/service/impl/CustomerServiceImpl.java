package com.sam.service.impl;

import com.sam.dao.ICustomerDAO;
import com.sam.dao.impl.CustomerDAOImpl;
import com.sam.entity.Customer;
import com.sam.service.ICustomerService;
import com.sam.util.ResultVO;

import java.util.List;

public class CustomerServiceImpl implements ICustomerService {

    // 步骤1：创建DAO层实现类对象
    private ICustomerDAO customerDAO = new CustomerDAOImpl();

    @Override
    public ResultVO<List<Customer>> findAllCustomers() {
        // 步骤2：调用DAO层方法获取数据
        List<Customer> list = customerDAO.selectAll();
        // 步骤3：封装结果返回
        //无论表中是否有数据，直接返回
            return ResultVO.success(list);
    }

    @Override
    public ResultVO registerCustomer(Customer customer) {
        // 步骤1:判断参数是否合规
        if(customer == null){
            return ResultVO.error("注册失败");
        }
        // 步骤2:调用DAO层方法
        int n = customerDAO.insertCustomer(customer);
        // 步骤3:封装结果返回
        return n > 0 ? ResultVO.success("录入成功") : ResultVO.error("注册失败");

    }
}