package com.sam.dao.impl;

import com.sam.dao.ICustomerDAO;
import com.sam.entity.Customer;
import com.sam.util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-06 19:52
 */
public class CustomerDAOImpl implements ICustomerDAO {
    @Override
    public List<Customer> selectAll() {
        List<Customer> list = new ArrayList<>();
        String sql = "select * from customer";

        try (
                Connection conn = DBUtil.getConnection();
                PreparedStatement ps = conn.prepareStatement(sql);
        ) {
            ResultSet rs = ps.executeQuery();
            //处理结果集
            while (rs.next()) {
                Customer customer = new Customer();
                customer.setId(rs.getInt("id"));
                customer.setName(rs.getString("name"));
                customer.setBirthday(rs.getDate("birthday"));
                customer.setSex(rs.getString("sex"));
                customer.setTel(rs.getString("tel"));
                customer.setAddress(rs.getString("address"));
                customer.setIsvip(rs.getInt("isvip"));
                customer.setRemark(rs.getString("remark"));
                customer.setCreatetime(rs.getDate("createtime"));
                customer.setUpdatetime(rs.getDate("updatetime"));
                list.add(customer);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public int insertCustomer(Customer customer) {
        int n = 0;
        String sql = "insert into Customer values(0,?,?,?,?,?,?,?,?,?)";
        try(Connection connection = DBUtil.getConnection();
            PreparedStatement ps = connection.prepareStatement(sql);
        ){
            ps.setString(1, customer.getName());
            ps.setDate(2, new java.sql.Date(customer.getBirthday().getTime()));
            ps.setString(3, customer.getSex());
            ps.setString(4, customer.getTel());
            ps.setString(5, customer.getAddress());
            ps.setInt(6, customer.getIsvip());
            ps.setString(7, customer.getRemark());
            ps.setDate(8, new java.sql.Date(customer.getCreatetime().getTime()));
            ps.setDate(9, new java.sql.Date(customer.getUpdatetime().getTime()));
            n = ps.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return n;
    }
}
