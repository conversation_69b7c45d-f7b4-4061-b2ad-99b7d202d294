/* style.css */



/* 容器居中 */
.button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh; /* 页面垂直居中 */
}

/* 按钮样式 */
.select-button {
    padding: 15px 30px;
    margin: 0 10px;
    font-size: 18px;
    border: 2px solid #007BFF;
    border-radius: 10px;
    background-color: white;
    color: #007BFF;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* 鼠标悬停效果 */
.select-button:hover {
    background-color: #007BFF;
    color: white;
}

/* 被选中状态 */
.select-button.active {
    background-color: #0056b3;
    color: white;
    border-color: #0056b3;
}