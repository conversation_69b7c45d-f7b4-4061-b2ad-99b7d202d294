<%--
  Created by IntelliJ IDEA.
  User: sam
  Date: 2025/8/6
  Time: 18:51
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ page isELIgnored="false" %>
<html>
<head>
    <title>顾客信息展示页面</title>
    <style>
        table {
            border-collapse: collapse;
            width: 80%;
            margin: 20px auto;  /* 居中对齐 */
            font-family: Arial, sans-serif;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #333;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f5f5f5;
        }

        a {
            text-decoration: none;
            color: #007bff;
            margin: 0 5px;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>

</head>


<body>

<h1 style="display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            color: #7168a2;"
    >
    顾客信息展示页面
</h1>

<c:if test="${r.data.size() == 0}">
<span style="color: red;">您目前没有添加任何顾客信息！</span>
</c:if>
<c:if test="${r.data.size() != 0}">
    <table>
        <tr>
            <th>编号</th>
            <th>名称</th>
            <th>生日</th>
            <th>性别</th>
            <th>电话</th>
            <th>地址</th>
            <th>vip</th>
            <th>操作</th>
        </tr>

        <c:forEach items="${r.data}" var="customer">
            <tr>
                <td>${customer.id}</td>
                <td>${customer.name}</td>
                <td><fmt:formatDate value="${customer.birthday}" pattern="yyyy-MM-dd"/></td>
                <td>${customer.sex}</td>
                <td>${customer.tel}</td>
                <td>${customer.address}</td>
                <td>${customer.isvip}</td>
                <th><a href="UpdateCustomerServlet">修改</a>
                    <a href="DeleteCustomerServlet">删除</a></th>
            </tr>
        </c:forEach>
    </table>
</c:if>
</body>
</html>
