<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<html>
<head>
    <title>新增顾客信息</title>
    <style>
        * {
            box-sizing: border-box;
            font-family: 'Segoe UI', sans-serif;
        }

        body {
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
        }

        h1 {
            text-align: center;
            color: #4a4a4a;
            margin-top: 40px;
            font-size: 24px;
        }

        .mid {
            width: 500px;
            margin: 40px auto;
            padding: 30px 40px;
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .form-group label {
            flex: 1;
            text-align: right;
            margin-right: 10px;
            color: #333;
            font-size: 14px;
        }

        .form-group input[type="text"],
        .form-group input[type="date"] {
            flex: 2;
            padding: 8px 10px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group .radio-group {
            flex: 2;
            display: flex;
            gap: 20px;
            padding-left: 10px;
        }

        .form-group .radio-group label {
            margin: 0;
            font-weight: normal;
            color: #333;
        }

        .required {
            color: red;
            margin-left: 2px;
        }

        .lastbutton {
            text-align: center;
            margin-top: 30px;
        }

        .lastbutton input[type="submit"] {
            width: 100%;
            padding: 12px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }

        .lastbutton input[type="submit"]:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>

<h1>顾客信息新增页面</h1>

<div class="mid">
    <form action="AddCustomerServlet" method="post">
        <div class="form-group">
            <label>真实姓名 <span class="required">*</span></label>
            <input type="text" name="name" placeholder="姓名，必填" required>
        </div>

        <div class="form-group">
            <label>生日 <span class="required">*</span></label>
            <input type="date" name="birthday" required>
        </div>

        <div class="form-group">
            <label>性别 <span class="required">*</span></label>
            <div class="radio-group">
                <label><input type="radio" name="sex" value="男" checked> 男</label>
                <label><input type="radio" name="sex" value="女"> 女</label>
            </div>
        </div>

        <div class="form-group">
            <label>手机号码 <span class="required">*</span></label>
            <input type="text" name="tel" placeholder="手机号" required>
        </div>

        <div class="form-group">
            <label>家庭地址 <span class="required">*</span></label>
            <input type="text" name="address" placeholder="地址" required>
        </div>

        <div class="form-group">
            <label>是否VIP <span class="required">*</span></label>
            <div class="radio-group">
                <label><input type="radio" name="isvip" value="1"> 是</label>
                <label><input type="radio" name="isvip" value="0" checked> 否</label>
            </div>
        </div>

        <div class="form-group">
            <label>备注</label>
            <input type="text" name="remark" placeholder="备注，选填">
        </div>

        <div class="lastbutton">
            <input type="submit" value="确认注册">
        </div>
    </form>
</div>

</body>
</html>